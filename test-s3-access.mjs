/**
 * Test script to verify S3 access for transcription results
 */

import { S3Client, ListObjectsV2Command, GetObjectCommand } from '@aws-sdk/client-s3';
import { config } from './config/environment.mjs';
import dotenv from 'dotenv';

dotenv.config();

async function testS3Access() {
  try {
    console.log('🔍 Testing S3 access for transcription results...\n');

    // Test S3 client configuration
    const s3Client = new S3Client({ 
      region: config.aws.s3.region,
      credentials: config.aws.s3credentials
    });

    const bucket = 'cravin-ai-temp';
    
    console.log(`📦 Testing bucket: ${bucket}`);
    console.log(`🌍 Region: ${config.aws.s3.region}`);
    console.log(`🔑 Using S3 credentials: ${config.aws.s3credentials.accessKeyId ? 'Yes' : 'No'}\n`);

    // Test 1: List objects in bucket
    console.log('1️⃣ Testing bucket access...');
    try {
      const listCommand = new ListObjectsV2Command({
        Bucket: bucket,
        MaxKeys: 5
      });
      
      const listResponse = await s3Client.send(listCommand);
      console.log(`✅ Bucket accessible. Found ${listResponse.KeyCount || 0} objects`);
      
      if (listResponse.Contents && listResponse.Contents.length > 0) {
        console.log('📁 Recent objects:');
        listResponse.Contents.slice(0, 3).forEach(obj => {
          console.log(`   - ${obj.Key} (${obj.Size} bytes)`);
        });
      }
      
    } catch (error) {
      console.log(`❌ Bucket access failed: ${error.message}`);
      return;
    }

    // Test 2: Look for transcription files
    console.log('\n2️⃣ Looking for transcription files...');
    try {
      const transcriptionCommand = new ListObjectsV2Command({
        Bucket: bucket,
        Prefix: 'transcriptions/',
        MaxKeys: 5
      });
      
      const transcriptionResponse = await s3Client.send(transcriptionCommand);
      
      if (transcriptionResponse.Contents && transcriptionResponse.Contents.length > 0) {
        console.log(`✅ Found ${transcriptionResponse.Contents.length} transcription files:`);
        
        // Try to read the most recent transcription file
        const latestFile = transcriptionResponse.Contents[0];
        console.log(`📄 Testing read access to: ${latestFile.Key}`);
        
        try {
          const getCommand = new GetObjectCommand({
            Bucket: bucket,
            Key: latestFile.Key
          });
          
          const getResponse = await s3Client.send(getCommand);
          
          // Convert stream to string
          const chunks = [];
          for await (const chunk of getResponse.Body) {
            chunks.push(chunk);
          }
          const content = Buffer.concat(chunks).toString('utf-8');
          
          console.log(`✅ Successfully read transcription file (${content.length} characters)`);
          console.log(`📝 Content preview: ${content.substring(0, 100)}...`);
          
          // Try to parse as JSON
          try {
            const jsonData = JSON.parse(content);
            if (jsonData.results && jsonData.results.transcripts) {
              console.log(`✅ Valid transcription JSON structure`);
              if (jsonData.results.transcripts[0]) {
                console.log(`🎤 Sample transcript: "${jsonData.results.transcripts[0].transcript}"`);
              }
            }
          } catch (parseError) {
            console.log(`❌ JSON parsing failed: ${parseError.message}`);
          }
          
        } catch (readError) {
          console.log(`❌ Failed to read transcription file: ${readError.message}`);
        }
        
      } else {
        console.log('ℹ️  No transcription files found yet');
      }
      
    } catch (error) {
      console.log(`❌ Error listing transcription files: ${error.message}`);
    }

    console.log('\n✅ S3 access test completed!');
    console.log('\n💡 Tips:');
    console.log('- If you see XML errors, check bucket permissions');
    console.log('- Ensure the S3 bucket exists in the specified region');
    console.log('- Verify AWS credentials have GetObject permissions');

  } catch (error) {
    console.error('❌ S3 access test failed:', error);
  }
}

testS3Access();
