/**
 * Audio Service
 * Handles audio message processing and speech-to-text conversion using AWS Transcribe
 */

import { TranscribeClient, StartTranscriptionJobCommand, GetTranscriptionJobCommand } from '@aws-sdk/client-transcribe';
import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { config } from '../config/environment.mjs';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

class AudioService {
  constructor() {
    this.transcribeClient = new TranscribeClient({ 
      region: config.aws.region 
    });
    this.s3Client = new S3Client({ 
      region: config.aws.region 
    });
    this.s3Bucket = config.aws.s3.transcribeBucket || 'cravin-concierge-audio-temp';
  }

  /**
   * Processes audio message and converts to text
   * @param {string} audioFilePath - Path to the downloaded audio file
   * @param {string} mediaId - WhatsApp media ID for unique identification
   * @returns {string} Transcribed text
   */
  async processAudioMessage(audioFilePath, mediaId) {
    try {
      console.log(`Processing audio file: ${audioFilePath}`);

      // Upload audio file to S3 temporarily
      const s3Key = await this.uploadToS3(audioFilePath, mediaId);
      
      // Start transcription job
      const transcriptionJobName = `whatsapp-audio-${mediaId}-${Date.now()}`;
      const transcriptionResult = await this.transcribeAudio(s3Key, transcriptionJobName);
      
      // Clean up S3 file
      await this.deleteFromS3(s3Key);
      
      // Clean up local file
      if (fs.existsSync(audioFilePath)) {
        fs.unlinkSync(audioFilePath);
      }
      
      return transcriptionResult;

    } catch (error) {
      console.error('Error processing audio message:', error);
      throw new Error('Failed to process audio message');
    }
  }

  /**
   * Uploads audio file to S3 for transcription
   * @param {string} audioFilePath - Local path to audio file
   * @param {string} mediaId - WhatsApp media ID
   * @returns {string} S3 key
   */
  async uploadToS3(audioFilePath, mediaId) {
    try {
      const s3Key = `temp-audio/${mediaId}-${uuidv4()}.ogg`;
      const fileContent = fs.readFileSync(audioFilePath);

      const command = new PutObjectCommand({
        Bucket: this.s3Bucket,
        Key: s3Key,
        Body: fileContent,
        ContentType: 'audio/ogg',
        // Set expiration for temporary files
        Expires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      });

      await this.s3Client.send(command);
      console.log(`Audio file uploaded to S3: ${s3Key}`);
      
      return s3Key;

    } catch (error) {
      console.error('Error uploading to S3:', error);
      throw error;
    }
  }

  /**
   * Transcribes audio using AWS Transcribe
   * @param {string} s3Key - S3 key of the audio file
   * @param {string} jobName - Unique job name
   * @returns {string} Transcribed text
   */
  async transcribeAudio(s3Key, jobName) {
    try {
      // Start transcription job
      const startCommand = new StartTranscriptionJobCommand({
        TranscriptionJobName: jobName,
        LanguageCode: 'en-US', // Default to English, can be made configurable
        MediaFormat: 'ogg',
        Media: {
          MediaFileUri: `s3://${this.s3Bucket}/${s3Key}`
        },
        OutputBucketName: this.s3Bucket,
        OutputKey: `transcriptions/${jobName}.json`
      });

      await this.transcribeClient.send(startCommand);
      console.log(`Transcription job started: ${jobName}`);

      // Poll for completion
      const transcribedText = await this.waitForTranscription(jobName);
      
      return transcribedText;

    } catch (error) {
      console.error('Error transcribing audio:', error);
      throw error;
    }
  }

  /**
   * Waits for transcription job to complete and returns the result
   * @param {string} jobName - Transcription job name
   * @returns {string} Transcribed text
   */
  async waitForTranscription(jobName, maxAttempts = 30, delayMs = 2000) {
    try {
      for (let attempt = 0; attempt < maxAttempts; attempt++) {
        const command = new GetTranscriptionJobCommand({
          TranscriptionJobName: jobName
        });

        const response = await this.transcribeClient.send(command);
        const job = response.TranscriptionJob;

        console.log(`Transcription job status: ${job.TranscriptionJobStatus}`);

        if (job.TranscriptionJobStatus === 'COMPLETED') {
          // Get the transcription result
          const transcriptUri = job.Transcript.TranscriptFileUri;
          const transcribedText = await this.getTranscriptionResult(transcriptUri);
          return transcribedText;
        } else if (job.TranscriptionJobStatus === 'FAILED') {
          throw new Error(`Transcription job failed: ${job.FailureReason}`);
        }

        // Wait before next attempt
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }

      throw new Error('Transcription job timed out');

    } catch (error) {
      console.error('Error waiting for transcription:', error);
      throw error;
    }
  }

  /**
   * Retrieves transcription result from S3
   * @param {string} transcriptUri - S3 URI of the transcript file
   * @returns {string} Transcribed text
   */
  async getTranscriptionResult(transcriptUri) {
    try {
      // Extract bucket and key from URI
      const url = new URL(transcriptUri);
      const bucket = url.hostname.split('.')[0];
      const key = url.pathname.substring(1);

      // Download transcript file
      const response = await fetch(transcriptUri);
      const transcriptData = await response.json();

      // Extract the transcribed text
      const transcribedText = transcriptData.results.transcripts[0].transcript;
      
      console.log(`Transcribed text: ${transcribedText}`);
      return transcribedText;

    } catch (error) {
      console.error('Error getting transcription result:', error);
      throw error;
    }
  }

  /**
   * Deletes temporary file from S3
   * @param {string} s3Key - S3 key to delete
   */
  async deleteFromS3(s3Key) {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.s3Bucket,
        Key: s3Key
      });

      await this.s3Client.send(command);
      console.log(`Deleted temporary file from S3: ${s3Key}`);

    } catch (error) {
      console.error('Error deleting from S3:', error);
      // Don't throw error for cleanup failures
    }
  }

  /**
   * Detects language from audio (future enhancement)
   * @param {string} audioFilePath - Path to audio file
   * @returns {string} Detected language code
   */
  async detectLanguage(audioFilePath) {
    // For now, return default language
    // This can be enhanced with AWS Transcribe's language identification
    return 'en-US';
  }
}

export const audioService = new AudioService();
