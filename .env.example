# WhatsApp Business API Configuration
WHATSAPP_VERIFY_TOKEN=your_verify_token_here
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_API_VERSION=v18.0
WHATSAPP_WEBHOOK_SECRET=your_webhook_secret

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key

# AWS Bedrock Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
BEDROCK_AGENT_ID=your_bedrock_agent_id
BEDROCK_AGENT_ALIAS_ID=TSTALIASID

# AWS S3 Configuration for Audio Processing
AWS_S3_TRANSCRIBE_BUCKET=your-s3-bucket-name
AWS_S3_REGION=me-central-1

# AWS Transcribe Configuration (must be in supported region)
TRANSCRIBE_LANGUAGE_CODE=en-US
TRANSCRIBE_REGION=us-east-1

# Database Configuration
DB_HOST=your_database_host
DB_PORT=5432
DB_NAME=cravin_concierge
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password
DB_SSL=true

# Application Configuration
NODE_ENV=development
LOG_LEVEL=info
