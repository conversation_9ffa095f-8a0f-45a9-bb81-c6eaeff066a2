# WhatsApp Business API Configuration
WHATSAPP_VERIFY_TOKEN=your_verify_token_here
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_API_VERSION=v18.0
WHATSAPP_WEBHOOK_SECRET=your_webhook_secret

# AWS Configuration
AWS_REGION=us-east-1

# AWS Bedrock Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
BEDROCK_AGENT_ID=your_bedrock_agent_id
BEDROCK_AGENT_ALIAS_ID=TSTALIASID

# AWS S3 Configuration for Audio Processing
AWS_S3_TRANSCRIBE_BUCKET=cravin-concierge-audio-temp

# AWS Transcribe Configuration
TRANSCRIBE_LANGUAGE_CODE=en-US

# Database Configuration
DB_HOST=your_database_host
DB_PORT=5432
DB_NAME=cravin_concierge
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password
DB_SSL=true

# Application Configuration
NODE_ENV=development
LOG_LEVEL=info
