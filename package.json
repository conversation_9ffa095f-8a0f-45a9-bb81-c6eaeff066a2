{"name": "cravin-concierge-whatsapp-lambda", "version": "1.0.0", "description": "WhatsApp webhook Lambda functions for Cravin Concierge with AI agent integration", "main": "index.mjs", "scripts": {"start": "node server.mjs", "dev": "nodemon server.mjs", "setup-db": "node setup-db.mjs", "test": "node test-webhook.mjs", "test:basic": "echo \"Error: no test specified\" && exit 1", "deploy": "zip -r function.zip . && aws lambda update-function-code --function-name cravin-whatsapp-webhook --zip-file fileb://function.zip"}, "keywords": ["whatsapp", "lambda", "aws", "bedrock", "ai", "vector-search", "postgresql"], "author": "Cravin Concierge", "license": "ISC", "dependencies": {"@aws-sdk/client-bedrock-agent-runtime": "^3.600.0", "@aws-sdk/client-bedrock-runtime": "^3.600.0", "@aws-sdk/client-s3": "^3.855.0", "@aws-sdk/client-transcribe": "^3.855.0", "axios": "^1.6.8", "crypto": "^1.0.1", "pg": "^8.11.3", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20.12.7", "@types/pg": "^8.11.6", "dotenv": "^16.4.5", "express": "^4.19.2", "nodemon": "^3.1.0", "serverless-offline": "^14.4.0"}}