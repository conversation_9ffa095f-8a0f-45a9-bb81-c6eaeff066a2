# Multi-Region AWS Setup for Audio Processing

## Problem
AWS Transcribe is not available in all regions. The `me-central-1` region (Middle East - UAE) does not support AWS Transcribe service, which causes the error:

```
Error: getaddrinfo ENOTFOUND transcribe.me-central-1.amazonaws.com
```

## Solution
Configure separate regions for different AWS services:
- **S3**: Use `me-central-1` for data locality and compliance
- **Transcribe**: Use `us-east-1` where the service is available
- **Bedrock**: Use appropriate region based on model availability

## Configuration

### Environment Variables (.env)
```bash
# AWS Credentials
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key

# S3 Configuration (can be in me-central-1)
AWS_S3_REGION=me-central-1
AWS_S3_TRANSCRIBE_BUCKET=cravin

# Transcribe Configuration (must be in supported region)
TRANSCRIBE_REGION=us-east-1
TRANSCRIBE_LANGUAGE_CODE=en-US

# Default AWS Region
AWS_REGION=us-east-1
```

### Updated Configuration Structure
The `config/environment.mjs` now supports:

```javascript
aws: {
  region: 'us-east-1', // Default region
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  },
  s3: {
    transcribeBucket: 'cravin',
    region: 'me-central-1' // S3 can be in your preferred region
  },
  transcribe: {
    languageCode: 'en-US',
    region: 'us-east-1' // Must be in supported region
  }
}
```

## AWS Transcribe Regional Availability

### ✅ Supported Regions
- `us-east-1` (N. Virginia) - **Recommended**
- `us-west-2` (Oregon)
- `eu-west-1` (Ireland)
- `ap-southeast-1` (Singapore)
- `ap-northeast-1` (Tokyo)

### ❌ Unsupported Regions
- `me-central-1` (UAE)
- `me-south-1` (Bahrain)
- `af-south-1` (Cape Town)
- Many others...

## Testing Regional Availability

Run the region checker script:
```bash
node check-transcribe-regions.mjs
```

This will test AWS Transcribe availability across different regions.

## Cross-Region Data Flow

1. **Audio Upload**: WhatsApp audio → S3 bucket in `me-central-1`
2. **Transcription**: Transcribe service in `us-east-1` reads from S3
3. **Results**: Transcription results stored back to S3
4. **Cleanup**: Temporary files deleted from S3

## IAM Permissions Required

Your AWS credentials need permissions for:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "transcribe:StartTranscriptionJob",
        "transcribe:GetTranscriptionJob",
        "transcribe:ListTranscriptionJobs"
      ],
      "Resource": "*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::cravin/*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "s3:ListBucket"
      ],
      "Resource": "arn:aws:s3:::cravin"
    }
  ]
}
```

## Cost Considerations

### Cross-Region Data Transfer
- **S3 to Transcribe**: Data transfer from `me-central-1` to `us-east-1`
- **Cost**: ~$0.02 per GB for cross-region transfer
- **Optimization**: Consider using Transcribe in a closer region if available

### Alternative Regions
If cost is a concern, consider these closer regions with Transcribe support:
- `eu-west-1` (Ireland) - Closer to Middle East
- `ap-southeast-1` (Singapore) - Alternative for Asia-Pacific

## Troubleshooting

### Common Issues
1. **ENOTFOUND Error**: Transcribe not available in region
   - Solution: Use `us-east-1` or other supported region

2. **Access Denied**: Insufficient IAM permissions
   - Solution: Add required S3 and Transcribe permissions

3. **Cross-Region Access**: S3 bucket not accessible from Transcribe
   - Solution: Ensure bucket policy allows cross-region access

### Debug Commands
```bash
# Test configuration
node test-audio.mjs

# Check region availability
node check-transcribe-regions.mjs

# Test S3 access
aws s3 ls s3://cravin --region me-central-1

# Test Transcribe access
aws transcribe list-transcription-jobs --region us-east-1
```

## Production Deployment

For production Lambda deployment:
1. Set environment variables in Lambda configuration
2. Ensure Lambda execution role has required permissions
3. Consider VPC configuration for security
4. Monitor cross-region data transfer costs
5. Set up CloudWatch logging for debugging

## Security Best Practices

1. **Credentials**: Use IAM roles instead of access keys in production
2. **Bucket Policy**: Restrict S3 access to specific services
3. **Encryption**: Enable S3 encryption for audio files
4. **Cleanup**: Implement automatic cleanup of old transcription files
5. **Monitoring**: Set up CloudWatch alarms for failed transcriptions
