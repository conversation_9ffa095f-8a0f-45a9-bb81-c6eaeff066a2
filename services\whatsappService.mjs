/**
 * WhatsApp Business API Service
 * Handles sending messages and managing WhatsApp interactions
 */

import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { config } from '../config/environment.mjs';

class WhatsAppService {
  constructor() {
    this.baseURL = `https://graph.facebook.com/${config.whatsapp.apiVersion}/${config.whatsapp.phoneNumberId}`;
    this.accessToken = config.whatsapp.accessToken;
  }

  /**
   * Sends a text message to a WhatsApp user
   * @param {string} to - Recipient phone number
   * @param {string} message - Message text to send
   * @returns {Object} API response
   */
  async sendTextMessage(to, message) {
    try {
      console.log(`Sending message to ${to}: ${message}`);

      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        type: 'text',
        text: {
          body: message
        }
      };

      const response = await axios.post(
        `${this.baseURL}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Message sent successfully:', response.data);
      return response.data;

    } catch (error) {
      console.error('Error sending WhatsApp message:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Sends a typing indicator to show the bot is processing
   * @param {string} to - Recipient phone number
   */
  async sendTypingIndicator(messageId) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        // recipient_type: 'individual',
        // to: to,
        // type: 'typing_indicator',
        "status":"read",
        message_id: messageId,
        typing_indicator: {
          type: 'text'
        }
      };

      await axios.post(
        `${this.baseURL}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      // console.log(`Typing indicator sent to ${to}`);

    } catch (error) {
      console.error('Error sending typing indicator:', error.response?.data || error.message);
      // Don't throw error for typing indicator failures
    }
  }

  /**
   * Marks a message as read
   * @param {string} messageId - ID of the message to mark as read
   */
  async markMessageAsRead(messageId) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        status: 'read',
        message_id: messageId
      };

      await axios.post(
        `${this.baseURL}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log(`Message ${messageId} marked as read`);

    } catch (error) {
      console.error('Error marking message as read:', error.response?.data || error.message);
      // Don't throw error for read receipt failures
    }
  }

  /**
   * Sends a template message
   * @param {string} to - Recipient phone number
   * @param {string} templateName - Name of the template
   * @param {string} languageCode - Language code (e.g., 'en_US')
   * @param {Array} components - Template components/parameters
   */
  async sendTemplateMessage(to, templateName, languageCode = 'en_US', components = []) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        type: 'template',
        template: {
          name: templateName,
          language: {
            code: languageCode
          },
          components: components
        }
      };

      const response = await axios.post(
        `${this.baseURL}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Template message sent successfully:', response.data);
      return response.data;

    } catch (error) {
      console.error('Error sending template message:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Sends a message with quick reply buttons
   * @param {string} to - Recipient phone number
   * @param {string} bodyText - Main message text
   * @param {Array} buttons - Array of button objects
   */
  async sendInteractiveMessage(to, bodyText, buttons) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        type: 'interactive',
        interactive: {
          type: 'button',
          body: {
            text: bodyText
          },
          action: {
            buttons: buttons.map((button, index) => ({
              type: 'reply',
              reply: {
                id: `btn_${index}`,
                title: button.title
              }
            }))
          }
        }
      };

      const response = await axios.post(
        `${this.baseURL}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Interactive message sent successfully:', response.data);
      return response.data;

    } catch (error) {
      console.error('Error sending interactive message:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Sends a CTA (Call-to-Action) message with a URL button
   * @param {string} to - Recipient phone number
   * @param {string} bodyText - Main message text
   * @param {string} buttonText - Text displayed on the button
   * @param {string} buttonUrl - URL attached to the button
   * @param {string} headerText - Header text (optional)
   * @param {string} footerText - Footer text (optional)
   * @returns {Object} API response
   */
  async sendCTAMessage(to, bodyText, buttonText, buttonUrl, headerText = "", footerText = "") {
    try {
      const payload = {
        messaging_product: "whatsapp",
        recipient_type: "individual",
        to: to,
        type: "interactive",
        interactive: {
          type: "cta_url",
          // Body
          body: {
            text: bodyText,
          },
          action: {
            name: "cta_url",
            parameters: {
              display_text: buttonText,
              url: buttonUrl,
            },
          },
        },
      };

      // Add header if provided
      if (headerText) {
        payload.interactive.header = {
          type: "text",
          text: headerText,
        };
      }

      // Add footer if provided
      if (footerText) {
        payload.interactive.footer = {
          text: footerText,
        };
      }

      const response = await axios.post(
        `${this.baseURL}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('CTA message sent successfully:', response.data);
      return response.data;

    } catch (error) {
      console.error('Error sending CTA message:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Gets media URL from media ID
   * @param {string} mediaId - Media ID from WhatsApp
   * @returns {string} Media URL
   */
  async getMediaUrl(mediaId) {
    try {
      const response = await axios.get(
        `https://graph.facebook.com/${config.whatsapp.apiVersion}/${mediaId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`
          }
        }
      );

      return response.data.url;

    } catch (error) {
      console.error('Error getting media URL:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Downloads media file from WhatsApp
   * @param {string} mediaId - Media ID from WhatsApp message
   * @returns {string} Path to downloaded file
   */
  async downloadMediaFile(mediaId) {
    try {
      console.log(`Downloading media file: ${mediaId}`);

      // Get media URL
      const mediaUrl = await this.getMediaUrl(mediaId);

      // Download the file
      const response = await axios.get(mediaUrl, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        },
        responseType: 'stream'
      });

      // Create temporary file path
      const tempDir = '/tmp';
      const fileName = `${mediaId}.ogg`; // WhatsApp voice messages are typically in OGG format
      const filePath = path.join(tempDir, fileName);

      // Ensure temp directory exists
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Save file to disk
      const writer = fs.createWriteStream(filePath);
      response.data.pipe(writer);

      return new Promise((resolve, reject) => {
        writer.on('finish', () => {
          console.log(`Media file downloaded: ${filePath}`);
          resolve(filePath);
        });
        writer.on('error', reject);
      });

    } catch (error) {
      console.error('Error downloading media file:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Gets media information including file type and size
   * @param {string} mediaId - Media ID from WhatsApp
   * @returns {Object} Media information
   */
  async getMediaInfo(mediaId) {
    try {
      const response = await axios.get(
        `https://graph.facebook.com/${config.whatsapp.apiVersion}/${mediaId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`
          }
        }
      );

      return {
        url: response.data.url,
        mimeType: response.data.mime_type,
        sha256: response.data.sha256,
        fileSize: response.data.file_size,
        id: response.data.id
      };

    } catch (error) {
      console.error('Error getting media info:', error.response?.data || error.message);
      throw error;
    }
  }
}

export const whatsappService = new WhatsAppService();
