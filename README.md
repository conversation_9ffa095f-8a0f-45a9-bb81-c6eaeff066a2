# Cravin Concierge WhatsApp Lambda Functions

This project contains AWS Lambda functions for handling WhatsApp webhook verification and message processing with AI agent integration using AWS Bedrock.

## Features

- **WhatsApp Webhook Verification**: Handles GET requests for webhook verification
- **Message Processing**: Processes incoming WhatsApp text messages with AI responses
- **Audio Message Support**: Converts WhatsApp voice notes to text using AWS Transcribe
- **AWS Bedrock Integration**: Uses AWS Bedrock agents with their own knowledge base
- **User Management**: Stores user names and phone numbers in PostgreSQL
- **Name Extraction**: Automatically extracts and stores user names from conversations
- **Rate Limiting**: Built-in rate limiting for message processing
- **Security**: Webhook signature validation and input sanitization

## Architecture

```
handlers/
├── webhookVerification.mjs    # GET request handler for webhook verification
└── messageHandler.mjs         # POST request handler for incoming messages

services/
├── aiService.mjs             # AWS Bedrock AI integration
├── audioService.mjs          # Audio processing and speech-to-text
├── userService.mjs           # User management and storage
└── whatsappService.mjs       # WhatsApp Business API integration

utils/
└── security.mjs             # Security utilities and validation

config/
└── environment.mjs          # Environment configuration management
```

## Setup

### 1. Environment Variables

Copy `.env.example` to `.env` and fill in your configuration:

```bash
cp .env.example .env
```

Required environment variables:
- `WHATSAPP_VERIFY_TOKEN`: Token for webhook verification
- `WHATSAPP_ACCESS_TOKEN`: WhatsApp Business API access token
- `WHATSAPP_PHONE_NUMBER_ID`: Your WhatsApp Business phone number ID
- `DB_HOST`, `DB_NAME`, `DB_USERNAME`, `DB_PASSWORD`: PostgreSQL database credentials

Optional environment variables for audio processing:
- `AWS_ACCESS_KEY_ID`: AWS access key ID
- `AWS_SECRET_ACCESS_KEY`: AWS secret access key
- `AWS_S3_TRANSCRIBE_BUCKET`: S3 bucket for temporary audio files
- `AWS_S3_REGION`: S3 bucket region (can be 'me-central-1')
- `TRANSCRIBE_LANGUAGE_CODE`: Language code for transcription (default: 'en-US')
- `TRANSCRIBE_REGION`: AWS Transcribe region (must be 'us-east-1' or supported region)
- `AWS_REGION`: Default AWS region (default: 'us-east-1')

### 2. Database Setup

Set up a PostgreSQL database for user storage. The application will automatically create the required tables on first run:

```sql
-- The application will create this table automatically
CREATE TABLE concierge_users (
  id SERIAL PRIMARY KEY,
  phone_number VARCHAR(20) UNIQUE NOT NULL,
  name VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 3. AWS Configuration

- Set up AWS credentials with access to Bedrock
- Optionally configure a Bedrock Agent for enhanced conversational AI

### 4. WhatsApp Business API Setup

1. Create a WhatsApp Business account
2. Set up a webhook endpoint pointing to your Lambda function
3. Configure the verify token and webhook secret

## Deployment

### Using AWS CLI

1. Install dependencies:
```bash
npm install
```

2. Create deployment package:
```bash
zip -r function.zip . -x "*.git*" "*.env*" "README.md"
```

3. Deploy to Lambda:
```bash
aws lambda update-function-code --function-name your-function-name --zip-file fileb://function.zip
```

### Environment Variables in Lambda

Set the following environment variables in your Lambda function configuration:
- All variables from `.env.example`
- Ensure `NODE_ENV=production`

## Usage

### Webhook Verification

The Lambda function automatically handles GET requests for webhook verification:

```
GET /webhook?hub.mode=subscribe&hub.verify_token=YOUR_TOKEN&hub.challenge=CHALLENGE
```

### Message Processing

POST requests with WhatsApp message data are processed automatically:

```json
{
  "entry": [{
    "changes": [{
      "field": "messages",
      "value": {
        "messages": [{
          "from": "1234567890",
          "type": "text",
          "text": {
            "body": "Hello, I'm looking for Italian restaurants"
          }
        }]
      }
    }]
  }]
}
```

## AI Features

The AI service provides:
- Natural language understanding of user queries
- AWS Bedrock agent integration with built-in knowledge base
- Contextual responses for local business recommendations
- Automatic user name extraction and storage
- Personalized conversation handling

## Audio Message Processing

The application supports WhatsApp voice notes and audio messages with a Lambda-optimized workflow:

1. **Message Reception**: Receives audio message from WhatsApp webhook
2. **Media Download**: Downloads audio file as buffer using WhatsApp Graph API
3. **Speech-to-Text**: Converts audio to text using AWS Transcribe (no file system required)
4. **AI Processing**: Processes transcribed text through the existing AI pipeline
5. **Response**: Sends AI-generated response back to the user

### Audio Processing Features

- **Automatic Transcription**: Converts voice notes to text using AWS Transcribe
- **Lambda-optimized**: No file system dependencies, works entirely with buffers
- **Language Support**: Configurable language detection (default: English)
- **Multiple Formats**: Supports OGG, MP3, and WAV audio formats
- **Memory Efficient**: Direct buffer-to-S3 processing
- **Duplicate Prevention**: Message deduplication prevents processing same audio multiple times
- **Error Handling**: Graceful fallback when audio processing fails
- **User Feedback**: Confirms successful audio transcription to users

### AWS Services Required

For audio processing, ensure you have:
- **AWS Transcribe**: For speech-to-text conversion (must be in supported region like `us-east-1`)
- **Amazon S3**: For temporary audio file storage (can be in `me-central-1`)
- **IAM Permissions**: For Transcribe and S3 access across regions

### Multi-Region Configuration

⚠️ **Important**: AWS Transcribe is not available in `me-central-1` region. The application supports multi-region setup:

- **S3 Storage**: Can use `me-central-1` for data locality
- **Transcribe Service**: Must use `us-east-1` or other supported regions
- **Cross-Region Access**: Automatic handling of data transfer between regions

See `MULTI_REGION_SETUP.md` for detailed configuration instructions.

## Security

- Webhook signature validation using HMAC-SHA256
- Input sanitization to prevent injection attacks
- Rate limiting to prevent abuse
- Secure database connections with SSL
- Secure handling of temporary audio files

## Monitoring

The application logs all important events and errors. Monitor your Lambda function logs in CloudWatch for:
- Incoming message processing
- AI response generation
- Database operations
- Error conditions

## Contributing

1. Follow the existing MVC architecture
2. Add proper error handling and logging
3. Update tests for new functionality
4. Ensure security best practices

## License

ISC License
