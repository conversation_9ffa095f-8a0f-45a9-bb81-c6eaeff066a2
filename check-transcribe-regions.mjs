/**
 * <PERSON><PERSON>t to check AWS Transcribe availability in different regions
 */

import { TranscribeClient, ListTranscriptionJobsCommand } from '@aws-sdk/client-transcribe';
import dotenv from 'dotenv';

dotenv.config();

const regions = [
  'us-east-1',
  'us-west-2', 
  'eu-west-1',
  'ap-southeast-1',
  'me-central-1'
];

async function checkTranscribeAvailability() {
  console.log('🔍 Checking AWS Transcribe availability in different regions...\n');

  for (const region of regions) {
    try {
      const client = new TranscribeClient({ 
        region: region,
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
        }
      });

      // Try to list transcription jobs to test if service is available
      const command = new ListTranscriptionJobsCommand({ MaxResults: 1 });
      await client.send(command);
      
      console.log(`✅ ${region}: AWS Transcribe is AVAILABLE`);
      
    } catch (error) {
      if (error.name === 'UnknownEndpoint' || error.code === 'ENOTFOUND') {
        console.log(`❌ ${region}: AWS Transcribe is NOT AVAILABLE`);
      } else {
        console.log(`⚠️  ${region}: ${error.message}`);
      }
    }
  }

  console.log('\n📋 Recommendations:');
  console.log('- Use us-east-1 or us-west-2 for AWS Transcribe (most reliable)');
  console.log('- Keep your S3 bucket in me-central-1 for data locality');
  console.log('- Configure separate regions for S3 and Transcribe services');
}

checkTranscribeAvailability();
